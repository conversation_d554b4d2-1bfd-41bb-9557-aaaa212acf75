import React, { createContext, useContext, useState, useEffect } from "react";

interface Workspace {
  id: string;
  name: string;
  logo?: string;
}

interface WorkspaceContextType {
  currentWorkspace: Workspace | null;
  workspaces: Workspace[];
  switchWorkspace: (workspaceId: string) => void;
  isLoading: boolean;
}

const WorkspaceContext = createContext<WorkspaceContextType | undefined>(
  undefined
);

export function useWorkspace() {
  const context = useContext(WorkspaceContext);
  if (context === undefined) {
    throw new Error("useWorkspace must be used within a WorkspaceProvider");
  }
  return context;
}

// Mock data for workspaces
const mockWorkspaces: Workspace[] = [
  {
    id: "1",
    name: "Engineering",
    logo: `data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiB2aWV3Qm94PSIwIDAgNDAgNDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxwYXRoIGQ9Ik0yNCAwSDE2VjEyLjA2MzJDMTUuOTY2MyAxNC4yNDM0IDE0LjE4ODUgMTYgMTIuMDAwNSAxNkgwVjI0SDguNjg2MjlDMTAuODA4IDI0IDEyLjg0MjkgMjMuMTU3MSAxNC4zNDMxIDIxLjY1NjlMMjEuNjU2OSAxNC4zNDMxQzIzLjE1NzEgMTIuODQyOSAyNCAxMC44MDggMjQgOC42ODYyOVYwWiIgZmlsbD0idXJsKCNwYWludDBfbGluZWFyXzc2OTdfODc4MikiPjwvcGF0aD4KPHBhdGggZD0iTTE2IDQwSDI0VjI3LjkzNjhDMjQuMDMzNyAyNS43NTY2IDI1LjgxMTUgMjQgMjcuOTk5NSAyNEg0MFYxNkgzMS4zMTM3QzI5LjE5MiAxNiAyNy4xNTcxIDE2Ljg0MjkgMjUuNjU2OSAxOC4zNDMxTDE4LjM0MzEgMjUuNjU2OUMxNi44NDI5IDI3LjE1NzEgMTYgMjkuMTkyIDE2IDMxLjMxMzdWNDBaIiBmaWxsPSJ1cmwoI3BhaW50MV9saW5lYXJfNzY5N184NzgyKSI+PC9wYXRoPgo8ZGVmcz4KPGxpbmVhckdyYWRpZW50IGlkPSJwYWludDBfbGluZWFyXzc2OTdfODc4MiIgeDE9IjIwIiB5MT0iLTAuOTk3MDk2IiB4Mj0iMjAiIHkyPSIzMy43OTMxIiBncmFkaWVudFVuaXRzPSJ1c2VyU3BhY2VPblVzZSI+CjxzdG9wIHN0b3AtY29sb3I9IiM3NUQ4RkMiPjwvc3RvcD4KPHN0b3Agb2Zmc2V0PSIxIiBzdG9wLWNvbG9yPSIjMDA3MkU1Ij48L3N0b3A+CjwvbGluZWFyR3JhZGllbnQ+CjxsaW5lYXJHcmFkaWVudCBpZD0icGFpbnQxX2xpbmVhcl83Njk3Xzg3ODIiIHgxPSIyMCIgeTE9Ii0wLjk5NzA5NiIgeDI9IjIwIiB5Mj0iMzMuNzkzMSIgZ3JhZGllbnRVbml0cz0idXNlclNwYWNlT25Vc2UiPgo8c3RvcCBzdG9wLWNvbG9yPSIjNzVEOEZDIj48L3N0b3A+CjxzdG9wIG9mZnNldD0iMSIgc3RvcC1jb2xvcj0iIzAwNzJFNSI+PC9zdG9wPgo8L2xpbmVhckdyYWRpZW50Pgo8L2RlZnM+Cjwvc3ZnPg==`,
  },
  { id: "2", name: "Marketing" },
  { id: "3", name: "Product" },
  { id: "4", name: "Design" },
];

interface WorkspaceProviderProps {
  children: React.ReactNode;
}

export function WorkspaceProvider({ children }: WorkspaceProviderProps) {
  const [currentWorkspace, setCurrentWorkspace] = useState<Workspace | null>(
    null
  );
  const [workspaces, setWorkspaces] = useState<Workspace[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Simulate loading workspaces from API or local storage
    const loadWorkspaces = async () => {
      setIsLoading(true);

      // Simulate API call delay
      await new Promise((resolve) => setTimeout(resolve, 500));

      // Load workspaces (mock data for now)
      setWorkspaces(mockWorkspaces);

      // Set default workspace (first one or from localStorage)
      const savedWorkspaceId = localStorage.getItem("current-workspace-id");
      const defaultWorkspace = savedWorkspaceId
        ? mockWorkspaces.find((w) => w.id === savedWorkspaceId) ||
          mockWorkspaces[0]
        : mockWorkspaces[0];

      setCurrentWorkspace(defaultWorkspace);
      setIsLoading(false);
    };

    loadWorkspaces();
  }, []);

  const switchWorkspace = (workspaceId: string) => {
    const workspace = workspaces.find((w) => w.id === workspaceId);
    if (workspace) {
      setCurrentWorkspace(workspace);
      localStorage.setItem("current-workspace-id", workspaceId);
    }
  };

  const value: WorkspaceContextType = {
    currentWorkspace,
    workspaces,
    switchWorkspace,
    isLoading,
  };

  return (
    <WorkspaceContext.Provider value={value}>
      {children}
    </WorkspaceContext.Provider>
  );
}

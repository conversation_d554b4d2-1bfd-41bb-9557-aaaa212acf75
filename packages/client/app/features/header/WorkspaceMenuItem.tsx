import { MenuItem, Avatar, Text, makeStyles, tokens } from "@fluentui/react-components";

const useStyles = makeStyles({
  workspaceItem: {
    display: "flex",
    alignItems: "center",
    gap: tokens.spacingHorizontalS,
  },
});

interface WorkspaceMenuItemProps {
  workspace: {
    id: string;
    name: string;
    logo?: string;
  };
  onSelect: (workspaceId: string) => void;
}

export function WorkspaceMenuItem({ workspace, onSelect }: WorkspaceMenuItemProps) {
  const styles = useStyles();

  return (
    <MenuItem
      key={workspace.id}
      onClick={() => onSelect(workspace.id)}
    >
      <div className={styles.workspaceItem}>
        <Avatar
          size={20}
          image={{
            src: workspace.logo,
          }}
        />
        <Text>{workspace.name}</Text>
      </div>
    </MenuItem>
  );
}

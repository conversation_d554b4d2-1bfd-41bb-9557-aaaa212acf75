import { useNavigate } from "react-router";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>u<PERSON><PERSON>,
  MenuItem,
  makeStyles,
  tokens,
} from "@fluentui/react-components";
import { useAuth } from "../auth/AuthContext";

const useStyles = makeStyles({
  userSection: {
    display: "flex",
    alignItems: "center",
    gap: tokens.spacingHorizontalM,
  },
});

export function UserMenu() {
  const styles = useStyles();
  const navigate = useNavigate();
  const { user, logout } = useAuth();

  const handleLogout = () => {
    logout();
    navigate("/login", { replace: true });
  };

  const getInitials = (name: string) => {
    return name
      .split(" ")
      .map((part) => part.charAt(0))
      .join("")
      .toUpperCase()
      .slice(0, 2);
  };

  return (
    <div className={styles.userSection}>
      <Menu>
        <MenuTrigger disableButtonEnhancement>
          <Avatar
            style={{ cursor: "pointer" }}
            name={user?.name}
            initials={user?.name ? getInitials(user.name) : "U"}
            size={32}
          />
        </MenuTrigger>

        <MenuPopover>
          <MenuList>
            <MenuItem onClick={handleLogout}>Sign Out</MenuItem>
          </MenuList>
        </MenuPopover>
      </Menu>
    </div>
  );
}

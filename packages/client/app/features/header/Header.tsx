import { useNavigate } from "react-router";
import {
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>u<PERSON>ist,
  MenuItem,
  MenuDivider,
  makeStyles,
  tokens,
  Persona,
  MenuButton,
} from "@fluentui/react-components";
import { useAuth } from "../auth/AuthContext";
import { useWorkspace } from "../workspace/WorkspaceContext";
import {
  ArrowExitFilled,
  ArrowExitRegular,
  bundleIcon,
  PeopleFilled,
  PeopleRegular,
  SettingsFilled,
  SettingsRegular,
} from "@fluentui/react-icons/svg";

const LogoutIcon = bundleIcon(ArrowExitFilled, ArrowExitRegular);
const TeamIcon = bundleIcon(PeopleFilled, PeopleRegular);
const SettingsIcon = bundleIcon(SettingsFilled, SettingsRegular);

const useStyles = makeStyles({
  header: {
    display: "flex",
    alignItems: "center",
    justifyContent: "space-between",
    padding: `${tokens.spacingVerticalM} ${tokens.spacingHorizontalL}`,
    backgroundColor: tokens.colorNeutralBackground1,
    borderBottom: `1px solid ${tokens.colorNeutralStroke2}`,
    height: "60px",
    boxSizing: "border-box",
  },
  workspaceSelector: {
    display: "flex",
    alignItems: "center",
    gap: tokens.spacingHorizontalS,
  },
  userSection: {
    display: "flex",
    alignItems: "center",
    gap: tokens.spacingHorizontalM,
  },
  workspaceLogo: {
    width: "24px",
    height: "24px",
    borderRadius: "4px",
    backgroundColor: tokens.colorBrandBackground,
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    color: tokens.colorNeutralForegroundOnBrand,
    fontWeight: "bold",
    fontSize: "12px",
  },
  workspaceItem: {
    display: "flex",
    alignItems: "center",
    gap: tokens.spacingHorizontalS,
  },
});

export function Header() {
  const styles = useStyles();
  const navigate = useNavigate();
  const { user, logout } = useAuth();
  const { currentWorkspace, workspaces, switchWorkspace } = useWorkspace();

  const handleLogout = () => {
    logout();
    navigate("/login", { replace: true });
  };

  const getInitials = (name: string) => {
    return name
      .split(" ")
      .map((part) => part.charAt(0))
      .join("")
      .toUpperCase()
      .slice(0, 2);
  };

  const handleWorkspaceSwitch = (workspaceId: string) => {
    switchWorkspace(workspaceId);
  };

  const handleSettings = () => {
    navigate("/settings");
  };

  const handleManageTeam = () => {
    navigate("/team");
  };

  return (
    <header className={styles.header}>
      <div className={styles.workspaceSelector}>
        <Menu>
          <MenuTrigger disableButtonEnhancement>
            <MenuButton appearance="transparent">
              <Persona
                textPosition="after"
                textAlignment="center"
                size="small"
                name={currentWorkspace?.name || "Workspace"}
                avatar={{
                  size: 24,
                  image: {
                    src: currentWorkspace?.logo,
                  },
                }}
              />
            </MenuButton>
          </MenuTrigger>
          <MenuPopover>
            <MenuList>
              <MenuItem onClick={handleSettings} icon={<SettingsIcon />}>
                Settings
              </MenuItem>
              <MenuItem onClick={handleManageTeam} icon={<TeamIcon />}>
                Manage Team
              </MenuItem>
              <MenuDivider />
              {workspaces.map((workspace) => (
                <MenuItem
                  key={workspace.id}
                  onClick={() => handleWorkspaceSwitch(workspace.id)}
                >
                  <div className={styles.workspaceItem}>
                    <Avatar
                      size={20}
                      image={{
                        src: workspace.logo,
                      }}
                    />
                    <Text>{workspace.name}</Text>
                  </div>
                </MenuItem>
              ))}
              <MenuDivider />
              <MenuItem onClick={handleLogout} icon={<LogoutIcon />}>
                Log Out
              </MenuItem>
            </MenuList>
          </MenuPopover>
        </Menu>
      </div>

      <div className={styles.userSection}>
        <Menu>
          <MenuTrigger disableButtonEnhancement>
            <Avatar
              style={{ cursor: "pointer" }}
              name={user?.name}
              initials={user?.name ? getInitials(user.name) : "U"}
              size={32}
            />
          </MenuTrigger>

          <MenuPopover>
            <MenuList>
              <MenuItem onClick={handleLogout}>Sign Out</MenuItem>
            </MenuList>
          </MenuPopover>
        </Menu>
      </div>
    </header>
  );
}

import { makeStyles, tokens } from "@fluentui/react-components";
import { WorkspaceSelector } from "./WorkspaceSelector";
import { UserMenu } from "./UserMenu";

const useStyles = makeStyles({
  header: {
    display: "flex",
    alignItems: "center",
    justifyContent: "space-between",
    padding: `${tokens.spacingVerticalM} ${tokens.spacingHorizontalL}`,
    backgroundColor: tokens.colorNeutralBackground1,
    borderBottom: `1px solid ${tokens.colorNeutralStroke2}`,
    height: "60px",
    boxSizing: "border-box",
  },
});

export function Header() {
  const styles = useStyles();

  return (
    <header className={styles.header}>
      <WorkspaceSelector />
      <UserMenu />
    </header>
  );
}

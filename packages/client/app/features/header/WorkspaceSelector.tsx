import { useNavigate } from "react-router";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  makeStyles,
  tokens,
} from "@fluentui/react-components";
import {
  PeopleFilled,
  PeopleRegular,
  SettingsFilled,
  SettingsRegular,
  ArrowExitFilled,
  ArrowExitRegular,
  bundleIcon,
} from "@fluentui/react-icons/svg";
import { useWorkspace } from "../workspace/WorkspaceContext";
import { useAuth } from "../auth/AuthContext";
import { WorkspaceMenuItem } from "./WorkspaceMenuItem";

const TeamIcon = bundleIcon(PeopleFilled, PeopleRegular);
const SettingsIcon = bundleIcon(SettingsFilled, SettingsRegular);
const LogoutIcon = bundleIcon(ArrowExitFilled, ArrowExitRegular);

const useStyles = makeStyles({
  workspaceSelector: {
    display: "flex",
    alignItems: "center",
    gap: tokens.spacingH<PERSON><PERSON><PERSON>S,
  },
});

export function WorkspaceSelector() {
  const styles = useStyles();
  const navigate = useNavigate();
  const { logout } = useAuth();
  const { currentWorkspace, workspaces, switchWorkspace } = useWorkspace();

  const handleLogout = () => {
    logout();
    navigate("/login", { replace: true });
  };

  const handleWorkspaceSwitch = (workspaceId: string) => {
    switchWorkspace(workspaceId);
  };

  const handleSettings = () => {
    navigate("/settings");
  };

  const handleManageTeam = () => {
    navigate("/team");
  };

  return (
    <div className={styles.workspaceSelector}>
      <Menu>
        <MenuTrigger disableButtonEnhancement>
          <MenuButton appearance="transparent">
            <Persona
              textPosition="after"
              textAlignment="center"
              size="small"
              name={currentWorkspace?.name || "Workspace"}
              avatar={{
                size: 24,
                image: {
                  src: currentWorkspace?.logo,
                },
              }}
            />
          </MenuButton>
        </MenuTrigger>
        <MenuPopover>
          <MenuList>
            <MenuItem onClick={handleSettings} icon={<SettingsIcon />}>
              Settings
            </MenuItem>
            <MenuItem onClick={handleManageTeam} icon={<TeamIcon />}>
              Manage Team
            </MenuItem>
            <MenuDivider />
            {workspaces.map((workspace) => (
              <WorkspaceMenuItem
                key={workspace.id}
                workspace={workspace}
                onSelect={handleWorkspaceSwitch}
              />
            ))}
            <MenuDivider />
            <MenuItem onClick={handleLogout} icon={<LogoutIcon />}>
              Log Out
            </MenuItem>
          </MenuList>
        </MenuPopover>
      </Menu>
    </div>
  );
}

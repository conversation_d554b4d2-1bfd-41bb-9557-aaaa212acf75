import {
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>eader,
  makeStyles,
  Text,
  tokens,
} from "@fluentui/react-components";

const useStyles = makeStyles({
  card: {
    width: "360px",
  },
  createButton: {
    marginTop: tokens.spacingVerticalL,
  },
});

export default function InitiativeListEmptyState() {
  const styles = useStyles();

  const handleCreateInitiative = () => {
    // TODO: Implement create initiative functionality
    console.log("Create initiative clicked");
  };

  return (
    <Card className={styles.card}>
      <CardHeader
        header={<Text weight="semibold">No Initiatives Yet</Text>}
      ></CardHeader>
      <Text size={300}>
        Start by creating your first strategic initiative to begin tracking
        progress toward your key objectives.
      </Text>
      <CardFooter>
        <Button
          appearance="primary"
          className={styles.createButton}
          onClick={handleCreateInitiative}
        >
          Create Initiative
        </Button>
      </CardFooter>
    </Card>
  );
}

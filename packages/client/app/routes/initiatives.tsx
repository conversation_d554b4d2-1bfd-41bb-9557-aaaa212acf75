import { useNavigate } from "react-router";
import type { Route } from "./+types/initiatives";
import {
  Text,
  Card,
  CardHeader,
  Button,
  Tag,
  Link,
  makeStyles,
  tokens,
  Table,
  TableBody,
  TableCell,
  TableRow,
  TableHeader,
  TableHeaderCell,
  TableCellLayout,
  Title1,
  Body2,
} from "@fluentui/react-components";
import { useInitiatives, type Initiative } from "../hooks/useInitiatives";
import InitiativeListEmptyState from "~/features/initiatives/InitiativeListEmptyState";

const useStyles = makeStyles({
  container: {
    display: "flex",
    flexDirection: "column",
    gap: tokens.spacingVerticalL,
    maxWidth: "1200px",
  },
  header: {
    marginBottom: tokens.spacingVerticalM,
  },
  tableContainer: {
    padding: tokens.spacingHorizontalL,
  },
  headerActions: {
    display: "flex",
    justifyContent: "space-between",
    alignItems: "center",
    width: "100%",
  },
  centerContent: {
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
    width: "100%",
    marginTop: tokens.spacingVerticalL,
  },
});

export function meta({}: Route.MetaArgs) {
  return [
    { title: "Initiatives - Vast" },
    {
      name: "description",
      content: "Track and manage your strategic initiatives",
    },
  ];
}

export default function Initiatives() {
  const styles = useStyles();
  const navigate = useNavigate();
  const { initiatives, loading, error } = useInitiatives();

  const getStatusBadge = (status: Initiative["status"]) => {
    return <Tag size="small">{status}</Tag>;
  };

  const getPriorityBadge = (priority: Initiative["priority"]) => {
    return <Tag size="small">{priority}</Tag>;
  };

  const handleCreateInitiative = () => {
    // TODO: Implement create initiative functionality
    console.log("Create initiative clicked");
  };

  if (loading) {
    return (
      <div className={styles.container}>
        <div className={styles.header}>
          <Title1 block>Initiatives</Title1>
          <Body2 block>Track and manage your strategic initiatives</Body2>
        </div>
        <div className={styles.centerContent}>
          <Text size={400}>Loading initiatives...</Text>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={styles.container}>
        <div className={styles.header}>
          <Title1 block>Initiatives</Title1>
          <Body2 block>Track and manage your strategic initiatives</Body2>
        </div>
        <div className={styles.centerContent}>
          <Card className={styles.card}>
            <CardHeader
              header={<Text weight="semibold">Error Loading Initiatives</Text>}
            ></CardHeader>
            <Text size={300} style={{ color: "red" }}>
              {error}
            </Text>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        {initiatives.length > 0 ? (
          <div className={styles.headerActions}>
            <div>
              <Title1 block>Initiatives</Title1>
              <Body2 block>Track and manage your strategic initiatives</Body2>
            </div>
            <Button appearance="primary" onClick={handleCreateInitiative}>
              Create Initiative
            </Button>
          </div>
        ) : (
          <div>
            <Title1 block>Initiatives</Title1>
            <Body2 block>Track and manage your strategic initiatives</Body2>
          </div>
        )}
      </div>

      {initiatives.length === 0 ? (
        // Empty state
        <div className={styles.centerContent}>
          <InitiativeListEmptyState />
        </div>
      ) : (
        <Table>
          <TableHeader>
            <TableRow>
              <TableHeaderCell>Initiative ID</TableHeaderCell>
              <TableHeaderCell>Initiative Name</TableHeaderCell>
              <TableHeaderCell>Status</TableHeaderCell>
              <TableHeaderCell>Priority</TableHeaderCell>
            </TableRow>
          </TableHeader>
          <TableBody>
            {initiatives.map((initiative) => (
              <TableRow
                key={initiative.id}
                onClick={() => navigate(`/initiatives/${initiative.id}`)}
              >
                <TableCell>
                  <TableCellLayout>
                    <Text weight="semibold">{initiative.initiativeId}</Text>
                  </TableCellLayout>
                </TableCell>
                <TableCell>
                  <TableCellLayout>
                    <Link
                      onClick={() => navigate(`/initiatives/${initiative.id}`)}
                      style={{ cursor: "pointer" }}
                    >
                      {initiative.name}
                    </Link>
                  </TableCellLayout>
                </TableCell>
                <TableCell>
                  <TableCellLayout>
                    {getStatusBadge(initiative.status)}
                  </TableCellLayout>
                </TableCell>
                <TableCell>
                  <TableCellLayout>
                    {getPriorityBadge(initiative.priority)}
                  </TableCellLayout>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      )}
    </div>
  );
}

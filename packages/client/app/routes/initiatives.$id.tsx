import { use<PERSON>ara<PERSON>, useNavigate } from "react-router";
import type { Route } from "./+types/initiatives.$id";
import {
  Text,
  Card,
  CardHeader,
  Button,
  Tag,
  makeStyles,
  tokens,
  Title1,
  Body1,
  Body2,
  Subtitle1,
} from "@fluentui/react-components";
import { useInitiative, type Initiative } from "../hooks/useInitiatives";

const useStyles = makeStyles({
  container: {
    display: "flex",
    flexDirection: "column",
    gap: tokens.spacingVerticalL,
    maxWidth: "1200px",
  },
  header: {
    marginBottom: tokens.spacingVerticalM,
  },
  backButton: {
    marginBottom: tokens.spacingVerticalM,
  },
  card: {
    width: "100%",
  },
  cardContent: {
    padding: tokens.spacingHorizontalL,
    display: "flex",
    flexDirection: "column",
    gap: tokens.spacingVerticalM,
  },
  detailRow: {
    display: "flex",
    flexDirection: "column",
    gap: tokens.spacingVerticalXS,
  },
  detailGrid: {
    display: "grid",
    gridTemplateColumns: "1fr 1fr",
    gap: tokens.spacingHorizontalL,
    "@media (max-width: 768px)": {
      gridTemplateColumns: "1fr",
    },
  },
  notFound: {
    textAlign: "center",
    padding: tokens.spacingVerticalXXL,
  },
});

export function meta({ params }: Route.MetaArgs) {
  return [
    { title: `Initiative ${params.id} - Vast` },
    { name: "description", content: "View initiative details" },
  ];
}

export default function InitiativeDetail() {
  const styles = useStyles();
  const { id } = useParams();
  const navigate = useNavigate();
  const { initiative, loading, error } = useInitiative(id || "");

  const getStatusTag = (status: Initiative["status"]) => {
    return <Tag size="small">{status}</Tag>;
  };

  const getPriorityTag = (priority: Initiative["priority"]) => {
    return <Tag size="small">{priority}</Tag>;
  };

  const handleBack = () => {
    navigate("/initiatives");
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return "Not set";
    return new Date(dateString).toLocaleDateString();
  };

  if (loading) {
    return (
      <div className={styles.container}>
        <Text size={400}>Loading...</Text>
      </div>
    );
  }

  if (error) {
    return (
      <div className={styles.container}>
        <Button
          appearance="subtle"
          onClick={handleBack}
          className={styles.backButton}
        >
          Back to Initiatives
        </Button>
        <Card className={styles.notFound}>
          <CardHeader>
            <Title1>Error Loading Initiative</Title1>
          </CardHeader>
          <div className={styles.cardContent}>
            <Body1 style={{ color: "red" }}>{error}</Body1>
            <Button appearance="primary" onClick={handleBack}>
              Return to Initiatives
            </Button>
          </div>
        </Card>
      </div>
    );
  }

  if (!initiative) {
    return (
      <div className={styles.container}>
        <Button
          appearance="subtle"
          onClick={handleBack}
          className={styles.backButton}
        >
          Back to Initiatives
        </Button>
        <Card className={styles.notFound}>
          <CardHeader>
            <Title1>Initiative Not Found</Title1>
          </CardHeader>
          <div className={styles.cardContent}>
            <Body1>The initiative with ID "{id}" could not be found.</Body1>
            <Button appearance="primary" onClick={handleBack}>
              Return to Initiatives
            </Button>
          </div>
        </Card>
      </div>
    );
  }

  return (
    <div className={styles.container}>
      <Button
        appearance="subtle"
        onClick={handleBack}
        className={styles.backButton}
      >
        Back to Initiatives
      </Button>

      <div className={styles.header}>
        <Title1 block>{initiative.name}</Title1>
        <Body2 block>{initiative.initiativeId}</Body2>
      </div>

      <Card className={styles.card}>
        <CardHeader>
          <Subtitle1>Initiative Details</Subtitle1>
        </CardHeader>
        <div className={styles.cardContent}>
          <div className={styles.detailRow}>
            <Text weight="semibold">Description</Text>
            <Body1>{initiative.description || "No description provided"}</Body1>
          </div>

          <div className={styles.detailGrid}>
            <div className={styles.detailRow}>
              <Text weight="semibold">Status</Text>
              {getStatusTag(initiative.status)}
            </div>
            <div className={styles.detailRow}>
              <Text weight="semibold">Priority</Text>
              {getPriorityTag(initiative.priority)}
            </div>
            <div className={styles.detailRow}>
              <Text weight="semibold">Owner</Text>
              <Body1>{initiative.owner || "Not assigned"}</Body1>
            </div>
            <div className={styles.detailRow}>
              <Text weight="semibold">Initiative ID</Text>
              <Body1>{initiative.initiativeId}</Body1>
            </div>
            <div className={styles.detailRow}>
              <Text weight="semibold">Start Date</Text>
              <Body1>{formatDate(initiative.startDate)}</Body1>
            </div>
            <div className={styles.detailRow}>
              <Text weight="semibold">End Date</Text>
              <Body1>{formatDate(initiative.endDate)}</Body1>
            </div>
          </div>
        </div>
      </Card>
    </div>
  );
}

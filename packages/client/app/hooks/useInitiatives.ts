import { useState, useEffect } from "react";

// Types
export interface Initiative {
  id: string;
  initiativeId: string;
  name: string;
  status: "Backlog" | "To do" | "In Progress" | "Done";
  priority: "Low" | "Medium" | "High" | "Not set";
  description?: string;
  owner?: string;
  startDate?: string;
  endDate?: string;
}

// Mock data - in a real app, this would come from an API
const mockInitiatives: Initiative[] = [
  {
    id: "1",
    initiativeId: "INT-001",
    name: "Customer Experience Enhancement",
    status: "In Progress",
    priority: "High",
    description:
      "Improve overall customer experience across all touchpoints including website, mobile app, and customer service interactions. This initiative aims to increase customer satisfaction scores by 25% and reduce customer churn by 15%.",
    owner: "<PERSON>",
    startDate: "2024-01-15",
    endDate: "2024-06-30",
  },
  {
    id: "2",
    initiativeId: "INT-002",
    name: "Digital Transformation Initiative",
    status: "To do",
    priority: "Medium",
    description:
      "Modernize legacy systems and implement new digital tools to improve operational efficiency and enable better data-driven decision making.",
    owner: "<PERSON>",
    startDate: "2024-03-01",
    endDate: "2024-12-31",
  },
  {
    id: "3",
    initiativeId: "INT-003",
    name: "Market Expansion Strategy",
    status: "Backlog",
    priority: "Low",
    description:
      "Explore and enter new geographic markets to expand our customer base and increase revenue streams.",
    owner: "Emily Rodriguez",
    startDate: "2024-07-01",
    endDate: "2025-03-31",
  },
  {
    id: "4",
    initiativeId: "INT-004",
    name: "Product Innovation Program",
    status: "Done",
    priority: "High",
    description:
      "Develop next-generation products that meet evolving customer needs and maintain competitive advantage in the market.",
    owner: "David Kim",
    startDate: "2023-09-01",
    endDate: "2024-02-29",
  },
];

// Simulate API delay
const delay = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms));

export function useInitiatives() {
  const [initiatives, setInitiatives] = useState<Initiative[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch all initiatives
  const fetchInitiatives = async () => {
    try {
      setLoading(true);
      setError(null);

      // Simulate API call delay
      await delay(500);

      // In a real app, this would be:
      // const response = await fetch('/api/initiatives');
      // const data = await response.json();
      // setInitiatives(data);

      setInitiatives(mockInitiatives);
    } catch (err) {
      setError(
        err instanceof Error ? err.message : "Failed to fetch initiatives"
      );
    } finally {
      setLoading(false);
    }
  };

  // Fetch single initiative by ID
  const fetchInitiativeById = async (
    id: string
  ): Promise<Initiative | null> => {
    try {
      // Simulate API call delay
      await delay(300);

      // In a real app, this would be:
      // const response = await fetch(`/api/initiatives/${id}`);
      // const data = await response.json();
      // return data;

      return mockInitiatives.find((initiative) => initiative.id === id) || null;
    } catch (err) {
      console.error("Failed to fetch initiative:", err);
      return null;
    }
  };

  // Create new initiative
  const createInitiative = async (
    initiativeData: Omit<Initiative, "id" | "initiativeId">
  ): Promise<Initiative> => {
    try {
      // Simulate API call delay
      await delay(500);

      // Generate new ID and initiative ID
      const newId = (mockInitiatives.length + 1).toString();
      const newInitiativeId = `INT-${String(
        mockInitiatives.length + 1
      ).padStart(3, "0")}`;

      const newInitiative: Initiative = {
        ...initiativeData,
        id: newId,
        initiativeId: newInitiativeId,
      };

      // In a real app, this would be:
      // const response = await fetch('/api/initiatives', {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify(initiativeData)
      // });
      // const data = await response.json();

      mockInitiatives.push(newInitiative);
      setInitiatives([...mockInitiatives]);

      return newInitiative;
    } catch (err) {
      throw new Error(
        err instanceof Error ? err.message : "Failed to create initiative"
      );
    }
  };

  // Update existing initiative
  const updateInitiative = async (
    id: string,
    updates: Partial<Initiative>
  ): Promise<Initiative | null> => {
    try {
      // Simulate API call delay
      await delay(500);

      const index = mockInitiatives.findIndex(
        (initiative) => initiative.id === id
      );
      if (index === -1) return null;

      const updatedInitiative = { ...mockInitiatives[index], ...updates };

      // In a real app, this would be:
      // const response = await fetch(`/api/initiatives/${id}`, {
      //   method: 'PUT',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify(updates)
      // });
      // const data = await response.json();

      mockInitiatives[index] = updatedInitiative;
      setInitiatives([...mockInitiatives]);

      return updatedInitiative;
    } catch (err) {
      throw new Error(
        err instanceof Error ? err.message : "Failed to update initiative"
      );
    }
  };

  // Delete initiative
  const deleteInitiative = async (id: string): Promise<boolean> => {
    try {
      // Simulate API call delay
      await delay(500);

      const index = mockInitiatives.findIndex(
        (initiative) => initiative.id === id
      );
      if (index === -1) return false;

      // In a real app, this would be:
      // const response = await fetch(`/api/initiatives/${id}`, {
      //   method: 'DELETE'
      // });

      mockInitiatives.splice(index, 1);
      setInitiatives([...mockInitiatives]);

      return true;
    } catch (err) {
      throw new Error(
        err instanceof Error ? err.message : "Failed to delete initiative"
      );
    }
  };

  // Load initiatives on mount
  useEffect(() => {
    fetchInitiatives();
  }, []);

  return {
    initiatives,
    loading,
    error,
    fetchInitiatives,
    fetchInitiativeById,
    createInitiative,
    updateInitiative,
    deleteInitiative,
  };
}

// Hook for fetching a single initiative
export function useInitiative(id: string) {
  const [initiative, setInitiative] = useState<Initiative | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchInitiative = async () => {
      try {
        setLoading(true);
        setError(null);

        // Simulate API call delay
        await delay(300);

        const found = mockInitiatives.find((init) => init.id === id);
        setInitiative(found || null);
      } catch (err) {
        setError(
          err instanceof Error ? err.message : "Failed to fetch initiative"
        );
      } finally {
        setLoading(false);
      }
    };

    if (id) {
      fetchInitiative();
    }
  }, [id]);

  return { initiative, loading, error };
}

import { reactRouter } from "@react-router/dev/vite";
import { defineConfig } from "vite";
import tsconfigPaths from "vite-tsconfig-paths";
import { cjsInterop } from "vite-plugin-cjs-interop";

export default defineConfig({
  ssr: {
    noExternal: ["@fluentui/react-icons"],
  },
  plugins: [
    reactRouter(),
    tsconfigPaths(),
    cjsInterop({
      dependencies: ["@fluentui/react-components", "@fluentui/react-icons"],
    }),
  ],
});
